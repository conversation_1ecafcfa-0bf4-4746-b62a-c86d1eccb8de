#@ load("@ytt:data", "data")

#@ load("commons.lib.yml",
#@   "testflight_group_name",
#@   "testflight_bootstrap_job_name",
#@   "testflight_inception_job_name",
#@   "testflight_platform_job_name",
#@   "testflight_postgresql_job_name",
#@   "testflight_smoketest_job_name",
#@   "testflight_cleanup_job_name",
#@   "testflight_cleanup_postgresql_job_name",
#@   "bump_repos_job_name",
#@   "build_pipeline_image_job_name",
#@   "common_resources",
#@   "resource_types")

#@ load("gcp.lib.yml",
#@   "gcp_jobs",
#@   "gcp_resources")

#@ load("azure.lib.yml",
#@   "azure_jobs",
#@   "azure_resources")

#@ supported_iaas = ["gcp", "azure"]
groups:
- name: all
  jobs:
#@ for cloud in supported_iaas:
  - #@ testflight_bootstrap_job_name(cloud)
  - #@ testflight_inception_job_name(cloud)
  - #@ testflight_platform_job_name(cloud)
  - #@ testflight_cleanup_job_name(cloud)
  - #@ bump_repos_job_name(cloud)
  - #@ testflight_postgresql_job_name(cloud)
  - #@ testflight_cleanup_postgresql_job_name(cloud)
  - #@ testflight_smoketest_job_name(cloud)
#@ end
#@ for/end cloud in supported_iaas:
  - #@ build_pipeline_image_job_name(cloud)
  - check-and-upgrade-k8s
#@ for cloud in supported_iaas:
- name: #@ testflight_group_name(cloud)
  jobs:
  - #@ testflight_bootstrap_job_name(cloud)
  - #@ testflight_inception_job_name(cloud)
  - #@ testflight_platform_job_name(cloud)
  - #@ testflight_cleanup_job_name(cloud)
  - #@ bump_repos_job_name(cloud)
  - #@ testflight_postgresql_job_name(cloud)
  - #@ testflight_cleanup_postgresql_job_name(cloud)
  - #@ testflight_smoketest_job_name(cloud)
#@ end
- name: image
  jobs:
#@ for/end cloud in supported_iaas:
  - #@ build_pipeline_image_job_name(cloud)
- name: k8s-upgrade
  jobs:
  - check-and-upgrade-k8s

jobs:
#@ for/end job in gcp_jobs():
- #@ job

#@ for/end job in azure_jobs():
- #@ job

- name: check-and-upgrade-k8s
  plan:
  - in_parallel:
    - get: every-1h
      trigger: true
    - get: repo
    - get: pipeline-tasks
  - task: check-and-upgrade-k8s
    config:
      platform: linux
      image_resource:
        type: registry-image
        source:
          username: #@ data.values.gar_registry_user
          password: #@ data.values.gar_registry_password
          repository: #@ data.values.docker_registry + "/galoy-deployments-pipeline"
      inputs:
      - name: repo
      - name: pipeline-tasks
      outputs:
      - name: repo
      params:
        BRANCH: #@ data.values.git_branch
        GOOGLE_CREDENTIALS: #@ data.values.testflight_gcp_creds
      run:
        path: pipeline-tasks/ci/tasks/check-and-upgrade-k8s.sh
  - put: repo
    params:
      repository: repo
      rebase: true

resources:
#@ for/end resource in common_resources():
- #@ resource

#@ for/end resource in gcp_resources():
- #@ resource

#@ for/end resource in azure_resources():
- #@ resource

- name: every-1h
  type: time
  icon: clock-outline
  source:
    interval: 1h

resource_types: #@ resource_types()
