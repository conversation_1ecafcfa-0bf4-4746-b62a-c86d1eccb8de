#@ load("@ytt:data", "data")

#@ load("commons.lib.yml",
#@   "testflight_bootstrap_job_name",
#@   "testflight_inception_job_name",
#@   "testflight_platform_job_name",
#@   "testflight_smoketest_job_name",
#@   "testflight_postgresql_job_name",
#@   "testflight_cleanup_job_name",
#@   "testflight_cleanup_postgresql_job_name",
#@   "bump_repos_job_name",
#@   "build_pipeline_image_job_name",
#@   "modules_resource_name")

#@ def pipeline_image():
#@   return data.values.docker_registry + "/azure-infra-pipeline"
#@ end

#@ def task_image_config():
type: registry-image
source:
  username: #@ data.values.gar_registry_user
  password: #@ data.values.gar_registry_password
  repository: #@ pipeline_image()
#@ end

#@ def azure_modules():
name: #@ modules_resource_name("azure")
type: git
source:
  paths:
  - modules/bootstrap/azure
  - modules/inception/azure
  - modules/platform/azure
  - modules/postgresql/azure
  - modules/smoketest
  uri: #@ data.values.git_uri
  branch: #@ data.values.git_branch
  private_key: #@ data.values.github_private_key
#@ end

#@ def azure_testflight_task_params():
ARM_CLIENT_ID: #@ data.values.testflight_azure_client_id
ARM_CLIENT_SECRET: #@ data.values.testflight_azure_client_secret
ARM_TENANT_ID: #@ data.values.testflight_azure_tenant_id
ARM_SUBSCRIPTION_ID: #@ data.values.testflight_azure_subscription_id
KUBE_HOST: #@ data.values.concourse_tf_kube_host
KUBE_CA_CERT: #@ data.values.concourse_tf_kube_ca_cert
KUBE_TOKEN: #@ data.values.concourse_tf_kube_token
TESTFLIGHT_ADMINS: #@ data.values.testflight_azure_admins
#@ end

#@ def azure_testflight_bootstrap_job():
name: #@ testflight_bootstrap_job_name("azure")
serial: true
plan:
- put: azure-testflight-lock
  params: { claim: azure-testflight }
- in_parallel:
  - get: #@ modules_resource_name("azure")
    trigger: true
  - { get: repo }
  - { get: pipeline-tasks }
  - { get: azure-testflight-uid }
- task: bootstrap
  config:
    platform: linux
    image_resource: #@ task_image_config()
    inputs:
    - name: pipeline-tasks
    - name: repo
    - name: #@ modules_resource_name("azure")
      path: modules
    - name: azure-testflight-uid
      path: testflight-uid
    params: #@ azure_testflight_task_params()
    run:
      path: pipeline-tasks/ci/tasks/azure/bootstrap.sh
#@ end

#@ def azure_testflight_inception_job():
name: #@ testflight_inception_job_name("azure")
serial: true
plan:
- get: azure-testflight-lock
  passed:
  - #@ testflight_bootstrap_job_name("azure")
- in_parallel:
  - get: #@ modules_resource_name("azure")
    trigger: true
    passed:
    - #@ testflight_bootstrap_job_name("azure")
  - get: repo
    passed:
    - #@ testflight_bootstrap_job_name("azure")
  - { get: pipeline-tasks }
  - { get: azure-testflight-uid }
- task: inception
  config:
    platform: linux
    image_resource: #@ task_image_config()
    inputs:
    - name: pipeline-tasks
    - name: repo
    - name: #@ modules_resource_name("azure")
      path: modules
    - name: azure-testflight-uid
      path: testflight-uid
    params: #@ azure_testflight_task_params()
    run:
      path: pipeline-tasks/ci/tasks/azure/inception.sh
#@ end

#@ def azure_testflight_platform_job():
name: #@ testflight_platform_job_name("azure")
serial: true
plan:
- get: azure-testflight-lock
  passed:
  - #@ testflight_inception_job_name("azure")
- in_parallel:
  - get: #@ modules_resource_name("azure")
    trigger: true
    passed:
    - #@ testflight_inception_job_name("azure")
  - get: repo
    passed:
    - #@ testflight_inception_job_name("azure")
  - { get: pipeline-tasks }
  - { get: azure-testflight-uid }
- task: platform
  config:
    platform: linux
    image_resource: #@ task_image_config()
    inputs:
    - name: pipeline-tasks
    - name: repo
    - name: #@ modules_resource_name("azure")
      path: modules
    - name: azure-testflight-uid
      path: testflight-uid
    params: #@ azure_testflight_task_params()
    run:
      path: pipeline-tasks/ci/tasks/azure/platform.sh
#@ end

#@ def azure_testflight_smoketest_job():
name: #@ testflight_smoketest_job_name("azure")
serial: true
plan:
- get: azure-testflight-lock
  passed:
  - #@ testflight_platform_job_name("azure")
- in_parallel:
  - get: #@ modules_resource_name("azure")
    trigger: true
    passed:
    - #@ testflight_platform_job_name("azure")
  - get: repo
    passed:
    - #@ testflight_platform_job_name("azure")
  - { get: pipeline-tasks }
  - get: azure-testflight-uid
    passed:
    - #@ testflight_platform_job_name("azure")
- task: smoketest
  config:
    platform: linux
    image_resource: #@ task_image_config()
    inputs:
    - name: pipeline-tasks
    - name: repo
    - name: #@ modules_resource_name("azure")
      path: modules
    - name: azure-testflight-uid
      path: testflight-uid
    params: #@ azure_testflight_task_params()
    run:
      path: pipeline-tasks/ci/tasks/azure/smoketest.sh
#@ end

#@ def azure_testflight_postgresql_job():
name: #@ testflight_postgresql_job_name("azure")
serial: true
plan:
- get: azure-testflight-lock
  passed:
  - #@ testflight_inception_job_name("azure")
- in_parallel:
  - get: #@ modules_resource_name("azure")
    trigger: true
    passed:
    - #@ testflight_inception_job_name("azure")
  - get: repo
    passed:
    - #@ testflight_inception_job_name("azure")
  - { get: pipeline-tasks }
  - { get: azure-testflight-uid }
- task: postgresql
  attempts: 4
  config:
    platform: linux
    image_resource: #@ task_image_config()
    inputs:
    - name: pipeline-tasks
    - name: repo
    - name: #@ modules_resource_name("azure")
      path: modules
    - name: azure-testflight-uid
      path: testflight-uid
    params: #@ azure_testflight_task_params()
    run:
      path: pipeline-tasks/ci/tasks/azure/postgresql.sh
#@ end

#@ def azure_testflight_cleanup_postgresql_job():
name: #@ testflight_cleanup_postgresql_job_name("azure")
serial: true
plan:
- get: azure-testflight-lock
  passed:
  - #@ testflight_postgresql_job_name("azure")
- in_parallel:
  - get: #@ modules_resource_name("azure")
    trigger: true
    passed:
    - #@ testflight_postgresql_job_name("azure")
  - get: repo
    passed:
    - #@ testflight_postgresql_job_name("azure")
  - { get: pipeline-tasks }
  - { get: azure-testflight-uid }
- task: teardown
  attempts: 6
  config:
    platform: linux
    image_resource: #@ task_image_config()
    inputs:
    - name: pipeline-tasks
    - name: repo
    - name: #@ modules_resource_name("azure")
      path: modules
    - name: azure-testflight-uid
      path: testflight-uid
    outputs:
    - name: repo
    params: #@ azure_testflight_task_params()
    run:
      path: pipeline-tasks/ci/tasks/azure/teardown-postgresql.sh
#@ end

#@ def azure_testflight_cleanup_job():
name: #@ testflight_cleanup_job_name("azure")
serial: true
plan:
- get: azure-testflight-lock
  passed:
  - #@ testflight_smoketest_job_name("azure")
  - #@ testflight_cleanup_postgresql_job_name("azure")
- in_parallel:
  - get: #@ modules_resource_name("azure")
    trigger: true
    passed:
    - #@ testflight_smoketest_job_name("azure")
    - #@ testflight_cleanup_postgresql_job_name("azure")
  - get: repo
    passed:
    - #@ testflight_smoketest_job_name("azure")
    - #@ testflight_cleanup_postgresql_job_name("azure")
  - { get: pipeline-tasks }
  - get: azure-testflight-uid
    passed:
    - #@ testflight_smoketest_job_name("azure")
    - #@ testflight_cleanup_postgresql_job_name("azure")
- task: teardown
  config:
    platform: linux
    image_resource: #@ task_image_config()
    inputs:
    - name: pipeline-tasks
    - name: repo
    - name: #@ modules_resource_name("azure")
      path: modules
    - name: azure-testflight-uid
      path: testflight-uid
    outputs:
    - name: repo
    params: #@ azure_testflight_task_params()
    run:
      path: pipeline-tasks/ci/tasks/azure/teardown.sh
- { put: azure-testflight-lock, params: { release: azure-testflight-lock }}
- { put: azure-testflight-uid, params: { bump: patch } }
#@ end

#@ def azure_bump_repos_job():
name: #@ bump_repos_job_name("azure")
plan:
- in_parallel:
  - { get: galoy-staging }
  - get: #@ modules_resource_name("azure")
    trigger: true
    passed:
    - #@ testflight_cleanup_job_name("azure")
  - get: repo
    trigger: true
    passed:
    - #@ testflight_cleanup_job_name("azure")
    params: { fetch_tags: false }
  - { get: pipeline-tasks }
  - get: azure-testflight-uid
    passed:
    - #@ testflight_cleanup_job_name("azure")
- task: bump-repos
  config:
    platform: linux
    image_resource: #@ task_image_config()
    inputs:
    - name: galoy-staging
    - name: pipeline-tasks
    - name: #@ modules_resource_name("azure")
      path: modules
    - name: repo
    - name: azure-testflight-uid
      path: testflight-uid
    outputs:
    - name: repo
    - name: galoy-staging
    params:
      BRANCH: #@ data.values.git_branch
      GITHUB_SSH_KEY: #@ data.values.github_private_key
    run:
      path: pipeline-tasks/ci/tasks/azure/bump-repos.sh
- in_parallel:
  - put: repo
    params:
      repository: repo
      rebase: true
  - put: galoy-staging
    params:
      repository: galoy-staging
      rebase: true
#@ end

#@ def build_pipeline_image_job():
name: #@ build_pipeline_image_job_name("azure")
serial: true
plan:
- {get: azure-pipeline-image-def, trigger: true}
- task: build
  privileged: true
  config:
    platform: linux
    image_resource:
      type: registry-image
      source:
        repository: vito/oci-build-task
    inputs:
    - name: azure-pipeline-image-def
    outputs:
    - name: image
    params:
      CONTEXT: azure-pipeline-image-def/ci/image/azure
    run:
      path: build
- put: azure-pipeline-image
  params:
    image: image/image.tar
#@ end

#@ def pipeline_image_resource():
name: azure-pipeline-image
type: registry-image
source:
  tag: latest
  username: #@ data.values.gar_registry_user
  password: #@ data.values.gar_registry_password
  repository: #@ pipeline_image()
#@ end

#@ def pipeline_image_def_resource():
name: azure-pipeline-image-def
type: git
source:
  paths: [ci/image/azure/Dockerfile]
  uri: #@ data.values.git_uri
  branch: #@ data.values.git_branch
  private_key: #@ data.values.github_private_key
#@ end

#@ def azure_testflight_uid_resource():
name: azure-testflight-uid
type: semver
source:
  initial_version: 0.0.0
  driver: git
  file: azure-version
  uri: #@ data.values.git_uri
  branch: #@ data.values.git_version_branch
  private_key: #@ data.values.github_private_key
#@ end

#@ def azure_testflight_lock_resource():
name: azure-testflight-lock
type: pool
source:
  uri: #@ data.values.concourse_locks_git_uri
  branch: main
  pool: azure-infra-testflight
  private_key: #@ data.values.github_private_key
#@ end

---

#@ def azure_jobs():
- #@ azure_testflight_bootstrap_job()
- #@ azure_testflight_inception_job()
- #@ azure_testflight_platform_job()
- #@ azure_testflight_smoketest_job()
- #@ azure_testflight_postgresql_job()
- #@ azure_testflight_cleanup_postgresql_job()
- #@ azure_testflight_cleanup_job()
- #@ azure_bump_repos_job()
- #@ build_pipeline_image_job()
#@ end

#@ def azure_resources():
- #@ azure_modules()
- #@ pipeline_image_resource()
- #@ pipeline_image_def_resource()
- #@ azure_testflight_uid_resource()
- #@ azure_testflight_lock_resource()
#@ end
