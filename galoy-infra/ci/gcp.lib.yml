#@ load("@ytt:data", "data")

#@ load("commons.lib.yml",
#@   "testflight_bootstrap_job_name",
#@   "testflight_inception_job_name",
#@   "testflight_platform_job_name",
#@   "testflight_postgresql_job_name",
#@   "testflight_smoketest_job_name",
#@   "testflight_cleanup_job_name",
#@   "testflight_cleanup_postgresql_job_name",
#@   "bump_repos_job_name",
#@   "build_pipeline_image_job_name",
#@   "modules_resource_name")

#@ def pipeline_image():
#@   return data.values.docker_registry + "/gcp-infra-pipeline"
#@ end

#@ def task_image_config():
type: registry-image
source:
  username: #@ data.values.gar_registry_user
  password: #@ data.values.gar_registry_password
  repository: #@ pipeline_image()
#@ end

#@ def gcp_modules():
name: #@ modules_resource_name("gcp")
type: git
webhook_token: ((webhook.secret))
source:
  paths:
  - modules/bootstrap/gcp
  - modules/inception/gcp
  - modules/platform/gcp
  - modules/postgresql/gcp
  - modules/smoketest
  uri: #@ data.values.git_uri
  branch: #@ data.values.git_branch
  private_key: #@ data.values.github_private_key
#@ end

#@ def gcp_testflight_task_params():
GOOGLE_CREDENTIALS: #@ data.values.testflight_gcp_creds
SSH_PRIVATE_KEY: #@ data.values.testflight_ssh_private_key
SSH_PUB_KEY: #@ data.values.testflight_ssh_pub_key
TESTFLIGHT_ADMINS: #@ data.values.testflight_admins
KUBE_HOST: #@ data.values.concourse_tf_kube_host
KUBE_CA_CERT: #@ data.values.concourse_tf_kube_ca_cert
KUBE_TOKEN: #@ data.values.concourse_tf_kube_token
TF_VAR_gcp_project: galoy-infra-testflight
TF_VAR_enable_services: false
BRANCH: #@ data.values.git_branch
#@ end

#@ def gcp_testflight_bootstrap_job():
name: #@ testflight_bootstrap_job_name("gcp")
serial: true
plan:
- put: gcp-testflight-lock
  params: { claim: gcp-testflight }
- in_parallel:
  - get: #@ modules_resource_name("gcp")
    trigger: true
  - { get: repo }
  - { get: pipeline-tasks }
  - { get: gcp-testflight-uid }
- task: bootstrap
  attempts: 4
  config:
    platform: linux
    image_resource: #@ task_image_config()
    inputs:
    - name: pipeline-tasks
    - name: repo
    - name: #@ modules_resource_name("gcp")
      path: modules
    - name: gcp-testflight-uid
      path: testflight-uid
    params: #@ gcp_testflight_task_params()
    run:
      path: pipeline-tasks/ci/tasks/gcp/bootstrap.sh
#@ end

#@ def gcp_testflight_inception_job():
name: #@ testflight_inception_job_name("gcp")
serial: true
plan:
- get: gcp-testflight-lock
  passed:
  - #@ testflight_bootstrap_job_name("gcp")
- in_parallel:
  - get: #@ modules_resource_name("gcp")
    trigger: true
    passed:
    - #@ testflight_bootstrap_job_name("gcp")
  - get: repo
    passed:
    - #@ testflight_bootstrap_job_name("gcp")
  - { get: pipeline-tasks }
  - get: gcp-testflight-uid
    passed:
    - #@ testflight_bootstrap_job_name("gcp")
- task: inception
  attempts: 4
  config:
    platform: linux
    image_resource: #@ task_image_config()
    inputs:
    - name: pipeline-tasks
    - name: repo
    - name: #@ modules_resource_name("gcp")
      path: modules
    - name: gcp-testflight-uid
      path: testflight-uid
    params: #@ gcp_testflight_task_params()
    run:
      path: pipeline-tasks/ci/tasks/gcp/inception.sh
#@ end

#@ def gcp_testflight_platform_job():
name: #@ testflight_platform_job_name("gcp")
serial: true
plan:
- get: gcp-testflight-lock
  passed:
  - #@ testflight_inception_job_name("gcp")
- in_parallel:
  - get: #@ modules_resource_name("gcp")
    trigger: true
    passed:
    - #@ testflight_inception_job_name("gcp")
  - get: repo
    passed:
    - #@ testflight_inception_job_name("gcp")
  - { get: pipeline-tasks }
  - get: gcp-testflight-uid
    passed:
    - #@ testflight_inception_job_name("gcp")
- task: platform
  config:
    platform: linux
    image_resource: #@ task_image_config()
    inputs:
    - name: pipeline-tasks
    - name: repo
    - name: #@ modules_resource_name("gcp")
      path: modules
    - name: gcp-testflight-uid
      path: testflight-uid
    params: #@ gcp_testflight_task_params()
    run:
      path: pipeline-tasks/ci/tasks/gcp/platform.sh
#@ end

#@ def gcp_testflight_smoketest_job():
name: #@ testflight_smoketest_job_name("gcp")
serial: true
plan:
- get: gcp-testflight-lock
  passed:
  - #@ testflight_platform_job_name("gcp")
- in_parallel:
  - get: #@ modules_resource_name("gcp")
    trigger: true
    passed:
    - #@ testflight_platform_job_name("gcp")
  - get: repo
    passed:
    - #@ testflight_platform_job_name("gcp")
  - { get: pipeline-tasks }
  - get: gcp-testflight-uid
    passed:
    - #@ testflight_platform_job_name("gcp")
- task: smoketest
  config:
    platform: linux
    image_resource: #@ task_image_config()
    inputs:
    - name: pipeline-tasks
    - name: repo
    - name: #@ modules_resource_name("gcp")
      path: modules
    - name: gcp-testflight-uid
      path: testflight-uid
    params: #@ gcp_testflight_task_params()
    run:
      path: pipeline-tasks/ci/tasks/gcp/smoketest.sh
#@ end

#@ def gcp_testflight_postgresql_job():
name: #@ testflight_postgresql_job_name("gcp")
serial: true
plan:
- get: gcp-testflight-lock
  passed:
  - #@ testflight_inception_job_name("gcp")
- in_parallel:
  - get: #@ modules_resource_name("gcp")
    trigger: true
    passed:
    - #@ testflight_inception_job_name("gcp")
  - get: repo
    passed:
    - #@ testflight_inception_job_name("gcp")
  - { get: pipeline-tasks }
  - get: gcp-testflight-uid
    passed:
    - #@ testflight_inception_job_name("gcp")
- task: postgresql
  attempts: 4
  config:
    platform: linux
    image_resource: #@ task_image_config()
    inputs:
    - name: pipeline-tasks
    - name: repo
    - name: #@ modules_resource_name("gcp")
      path: modules
    - name: gcp-testflight-uid
      path: testflight-uid
    params: #@ gcp_testflight_task_params()
    run:
      path: pipeline-tasks/ci/tasks/gcp/postgresql.sh
#@ end

#@ def gcp_testflight_cleanup_job():
name: #@ testflight_cleanup_job_name("gcp")
serial: true
plan:
- get: gcp-testflight-lock
  passed:
  - #@ testflight_smoketest_job_name("gcp")
  - #@ testflight_cleanup_postgresql_job_name("gcp")
- in_parallel:
  - get: #@ modules_resource_name("gcp")
    trigger: true
    passed:
    - #@ testflight_smoketest_job_name("gcp")
    - #@ testflight_cleanup_postgresql_job_name("gcp")
  - get: repo
    passed:
    - #@ testflight_smoketest_job_name("gcp")
    - #@ testflight_cleanup_postgresql_job_name("gcp")
  - { get: pipeline-tasks }
  - get: gcp-testflight-uid
    passed:
    - #@ testflight_smoketest_job_name("gcp")
    - #@ testflight_cleanup_postgresql_job_name("gcp")
- task: teardown
  config:
    platform: linux
    image_resource: #@ task_image_config()
    inputs:
    - name: pipeline-tasks
    - name: repo
    - name: #@ modules_resource_name("gcp")
      path: modules
    - name: gcp-testflight-uid
      path: testflight-uid
    outputs:
    - name: repo
    params: #@ gcp_testflight_task_params()
    run:
      path: pipeline-tasks/ci/tasks/gcp/teardown.sh
- in_parallel:
  - { put: gcp-testflight-lock, params: { release: gcp-testflight-lock }}
  - { put: gcp-testflight-uid, params: { bump: patch } }
#@ end

#@ def gcp_testflight_cleanup_postgresql_job():
name: #@ testflight_cleanup_postgresql_job_name("gcp")
serial: true
plan:
- get: gcp-testflight-lock
  passed:
  - #@ testflight_postgresql_job_name("gcp")
- in_parallel:
  - get: #@ modules_resource_name("gcp")
    trigger: true
    passed:
    - #@ testflight_postgresql_job_name("gcp")
  - get: repo
    passed:
    - #@ testflight_postgresql_job_name("gcp")
  - { get: pipeline-tasks }
  - get: gcp-testflight-uid
    passed:
    - #@ testflight_postgresql_job_name("gcp")
- task: teardown
  attempts: 6
  config:
    platform: linux
    image_resource: #@ task_image_config()
    inputs:
    - name: pipeline-tasks
    - name: repo
    - name: #@ modules_resource_name("gcp")
      path: modules
    - name: gcp-testflight-uid
      path: testflight-uid
    outputs:
    - name: repo
    params: #@ gcp_testflight_task_params()
    run:
      path: pipeline-tasks/ci/tasks/gcp/teardown-postgresql.sh
#@ end

#@ def gcp_bump_repos_job():
name: #@ bump_repos_job_name("gcp")
plan:
- in_parallel:
  - { get: galoy-staging }
  - get: #@ modules_resource_name("gcp")
    trigger: true
    passed:
    - #@ testflight_cleanup_job_name("gcp")
  - get: repo
    trigger: true
    passed:
    - #@ testflight_cleanup_job_name("gcp")
    params: { fetch_tags: false }
  - { get: pipeline-tasks }
  - get: gcp-testflight-uid
    passed:
    - #@ testflight_cleanup_job_name("gcp")
- task: bump-repos
  config:
    platform: linux
    image_resource: #@ task_image_config()
    inputs:
    - name: galoy-staging
    - name: pipeline-tasks
    - name: #@ modules_resource_name("gcp")
      path: modules
    - name: repo
    - name: gcp-testflight-uid
      path: testflight-uid
    outputs:
    - name: repo
    - name: galoy-staging
    params:
      BRANCH: #@ data.values.git_branch
      GITHUB_SSH_KEY: #@ data.values.github_private_key
    run:
      path: pipeline-tasks/ci/tasks/gcp/bump-repos.sh
- in_parallel:
  - put: repo
    params:
      repository: repo
      rebase: true
  - put: galoy-staging
    params:
      repository: galoy-staging
      rebase: true
#@ end

#@ def build_pipeline_image_job():
name: #@ build_pipeline_image_job_name("gcp")
serial: true
plan:
- {get: gcp-pipeline-image-def, trigger: true}
- task: build
  config:
    platform: linux
    image_resource:
      type: registry-image
      source:
        repository: gcr.io/kaniko-project/executor
        tag: debug
    inputs:
    - name: gcp-pipeline-image-def
    outputs:
    - name: image
    run:
      path: /kaniko/executor
      args:
      - --context=gcp-pipeline-image-def/ci/image/gcp
      - --use-new-run
      - --single-snapshot
      - --cache=false
      - --no-push
      - --tar-path=image/image.tar
- put: gcp-pipeline-image
  params:
    image: image/image.tar
#@ end

#@ def pipeline_image_resource():
name: gcp-pipeline-image
type: registry-image
source:
  tag: latest
  username: #@ data.values.gar_registry_user
  password: #@ data.values.gar_registry_password
  repository: #@ pipeline_image()
#@ end

#@ def pipeline_image_def_resource():
name: gcp-pipeline-image-def
type: git
source:
  paths: [ci/image/gcp/Dockerfile]
  uri: #@ data.values.git_uri
  branch: #@ data.values.git_branch
  private_key: #@ data.values.github_private_key
#@ end

#@ def gcp_testflight_uid_resource():
name: gcp-testflight-uid
type: semver
source:
  initial_version: 0.0.0
  driver: git
  file: version
  uri: #@ data.values.git_uri
  branch: #@ data.values.git_version_branch
  private_key: #@ data.values.github_private_key
#@ end

#@ def gcp_testflight_lock_resource():
name: gcp-testflight-lock
type: pool
source:
  uri: #@ data.values.concourse_locks_git_uri
  branch: main
  pool: gcp-infra-testflight
  private_key: #@ data.values.github_private_key
#@ end

---

#@ def gcp_jobs():
- #@ gcp_testflight_bootstrap_job()
- #@ gcp_testflight_inception_job()
- #@ gcp_testflight_platform_job()
- #@ gcp_testflight_smoketest_job()
- #@ gcp_testflight_postgresql_job()
- #@ gcp_testflight_cleanup_postgresql_job()
- #@ gcp_testflight_cleanup_job()
- #@ gcp_bump_repos_job()
- #@ build_pipeline_image_job()
#@ end

#@ def gcp_resources():
- #@ gcp_modules()
- #@ pipeline_image_resource()
- #@ pipeline_image_def_resource()
- #@ gcp_testflight_uid_resource()
- #@ gcp_testflight_lock_resource()
#@ end
