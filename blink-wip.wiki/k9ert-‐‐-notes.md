# Miscellaneous

[toc]


## Links

* Staging dashboard: https://admin.staging.galoy.io/account
* mattermost galoy: https://chat.galoy.io/galoy/
* mattermost blink: https://chat.blink.sv/
* developer documentation: https://dev.galoy.io/deployment
* onboarding: https://github.com/blinkbitcoin/onboarding/blob/main/README.md
* cepler: https://github.com/bodymindarts/cepler (https://www.loom.com/share/9b0b4a254bd646de9285c98404e21aca)
* project Management: https://github.com/orgs/blinkbitcoin/projects/1/views/1
* dev sync notes: https://docs.google.com/document/d/1jVgIuYQKvgzAra_ao0D7ibJu-pLiJDM0_JdseCxkxHI/edit?pli=1&tab=t.0#heading=h.jvl4knmcv4l3
* https://www.youtube.com/watch?v=j1Anp6vWQP0&t=22s (continue at min 49)
* https://www.youtube.com/watch?v=yTkzNHF6rMs (continue at min 19)
* Staging dashboard: https://dashboard.staging.blink.sv/

## Data Analytics
* https://console.firebase.google.com/u/0/project/galoyapp/overview?pli=1
* Holistics has a unique access concept. At least i only have access via the passwords stored in [this google document](https://docs.google.com/document/d/1QFnszbDRj0d41ZJhZBpuOvemAkmFSFsgjpj4qCwNBtI/edit?tab=t.0)
* https://analytics.google.com/
* Managing those IDs which are used in websites and apps to upload data for google analytics: https://tagmanager.google.com/#/home#accounts

## Lightning Nodes
* lnd1: https://lightningnetwork.plus/nodes/0325bb9bda523a85dc834b190289b7e25e8d92615ab2f2abffbe97983f0bb12ffb
* lnd2: https://lightningnetwork.plus/nodes/02fcc5bfc48e83f06c04483a2985e1c390cb0f35058baa875ad2053858b8e80dbd
* signet lnd1 https://mempool.space/signet/lightning/node/024e679c1a77143029b806f396f935fa6cd0744970f412667adfc75edbbab54d7a
* signet lnd2 https://mempool.space/signet/lightning/node/03bb03bb6e389355834c9fc7dfeb849dab17d9940d955f6dba0c27e84c88ca4ab8 

## Links 3rd party
* Visibility: https://ui.honeycomb.io/galoy/
* AppStore: https://appstoreconnect.apple.com/apps/**********/distribution/ios/version/deliverable
* PlayStore: https://play.google.com/console/u/0/developers/8350101450647692243/app-list
* Huawei Store: https://developer.huawei.com/consumer/en/service/josp/agc/index.html#/

## Testing and Testaccounts
## Mobile App
use [Obtainium](https://github.com/ImranR98/Obtainium) to install from github [pre-releases](https://github.com/blinkbitcoin/blink-mobile/releases)

### Staging environment
* visit https://dashboard.staging.blink.sv/
* Use a regular existing telephone number and the SMS code 403370 to get an account

## nostrudel nostr
https://nostrudel.ninja/#/
nsec1sny7f72977kmujwnwna6rh2natwxtaafe6r93mg77x53kh094l8sslnnxv (password: ups)

## signet laisee wallet

https://signet.laisee.org/wallet?wal=81b0f19a123143a198cc2a751c3cd2f3



## Google projects
* blink-reports

# Authentication

This section should give an overview of each and every human and service authentication, but mostly about service auth. It might only be a strong start.

## Concourse against vault

This section is now merged into the blinkbitcoin/blink-org-inra, [here](https://github.com/blinkbitcoin/blink-org-infra/blob/c2b3e7bfa172e5cb926103877068000d219b7b9f/docs/concourse.md#authenticate-against-vault)



