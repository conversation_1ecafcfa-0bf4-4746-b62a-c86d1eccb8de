Today we were puzzled by an unintended upgrade of the staging-cluster.

# Staging cluster
That upgrade happened in [galoy-staging-platform/builds/361](https://ci.blink.sv/teams/dev/pipelines/blink-staging/jobs/galoy-staging-platform/builds/361) and was triggered by this [commit](https://github.com/search?q=org%3Ablinkbitcoin+efe842644097b301e6cff948772c592a80812d41&type=commits). However, this commit did not mention anything about a k8s-upgrade. The status of staging is basically defined by the state of the main branch, more specifically in [/modules/infra/vendor/tf/platform/gcp/variables.tf](https://github.com/blinkbitcoin/blink-deployments/blob/a66a38e0b10003d3184da853c2f7b484ef0a017f/modules/infra/vendor/tf/platform/gcp/variables.tf#L12-L14): 

So basically a staging deployment of a specific job will always deploy the relevant changes as they are shown in the main branch and that version was `1.30.9-gke.1231000`. So there are now two questions:
* Why did that job even got triggered ?
* Why does it deploy a version which is nowhere specified?

## Why it got triggered

It's unclear to me where the code is which deploys the changes done in the above commit but that's not in the focus of this writeup. The change got triggered by the `galoy-staging-infra` resource which is of type `cepler-in` and defined like this (`fly get-pipeline -t ciblink  -p blink-staging | less `) :

```yaml
- name: galoy-staging-infra
  source:
    branch: main
    config: cepler/infra.yml
    environment: gcp-galoy-staging
    gates_branch: cepler-gates
    gates_file: cepler-gates/infra.yml
    private_key: ((github-blinkbitcoin.private_key))
    uri: **************:blinkbitcoin/blink-deployments.git
  type: cepler-in
```

A [cepler-concourse-resource](https://github.com/bodymindarts/cepler/tree/main/concourse) implements the [cepler-idea](https://www.loom.com/share/9b0b4a254bd646de9285c98404e21aca) for concourse. The above definition specifies `infra.yml` which looks like this:

```yaml
deployment: infra
environments:
  gcp-galoy-staging:
    latest:
    - modules/infra/**

    - gcp/galoy-staging/shared/*
    - gcp/galoy-staging/inception/*
    - gcp/galoy-staging/platform/*
    - gcp/galoy-staging/smoketest/*

  gcp-galoy-bbw:
    passed: gcp-galoy-staging
    ignore_queue: true
    propagated:
    - modules/infra/**
    latest:
    - gcp/galoy-bbw/shared/*
    - gcp/galoy-bbw/inception/*
    - gcp/galoy-bbw/platform/*
    - gcp/galoy-bbw/smoketest/*

  infra-release:
    passed: gcp-galoy-bbw
    propagated:
      - modules/infra/vendor/git-ref/ref
```

So the module change mentioned above is somehow lumped together with other parts of the repo. That resource is not only used by galoy-staging-platform but also galoy-staging-inception. Anyway, the line `- gcp/galoy-staging/shared/*` and the above commit triggered it.

## Why that newer version?

The older `1.30.x` version get injected in the terraform resource [here](https://github.com/blinkbitcoin/blink-deployments/blob/a66a38e0b10003d3184da853c2f7b484ef0a017f/modules/infra/vendor/tf/platform/gcp/kube.tf#L15):

```tf
resource "google_container_cluster" "primary" {
  provider = google-beta

  deletion_protection = !local.destroyable_cluster
  min_master_version  = local.kube_version
  name                = local.cluster_name
  description         = "Cluster hosting the ${local.name_prefix} apps"
  project             = local.project

  location = local.cluster_location
  network  = data.google_compute_network.vpc.self_link

  release_channel {
    channel = "UNSPECIFIED"
  }
...
```

The `min_master_version` is documented [here](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/container_cluster#min_master_version-1) and reads:

> min_master_version - (Optional) The minimum version of the master. GKE will auto-update the master to new versions, so this does not guarantee the current master version--use the read-only master_version field to obtain that. If unset, the cluster's version will be set by GKE to the version of the most recent official release (which is not necessarily the latest version).

But that's only the default-variable. Did it got overridden? No, the [/gcp/galoy-staging/platform/main.tf](https://github.com/blinkbitcoin/blink-deployments/blob/a66a38e0b10003d3184da853c2f7b484ef0a017f/gcp/galoy-staging/platform/main.tf#L26) doesn't do that:

```tf
module "platform" {
  source = "../../../modules/infra/vendor/tf/platform/gcp"

  node_default_machine_type = "n2-standard-4"
  node_service_account      = "<EMAIL>"
  max_default_node_count    = 3
  name_prefix               = module.shared.name_prefix
  gcp_project               = module.shared.gcp_project
  region                    = module.shared.gcp_region
  deploy_lnd_ips            = true

  pg_ha = true
}
```

... neither with 'min_master_version` nor the `release_channel`.

Looking at the [release_channel](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/container_cluster#release_channel-1) reads:

> [release_channel](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/container_cluster#release_channel-1) - (Optional) Configuration options for the [Release channel](https://cloud.google.com/kubernetes-engine/docs/concepts/release-channels) feature, which provide more control over automatic upgrades of your GKE clusters. When updating this field, GKE imposes specific version requirements. See [Selecting a new release channel](https://cloud.google.com/kubernetes-engine/docs/concepts/release-channels#selecting_a_new_release_channel) for more details; the google_container_engine_versions datasource can provide the default version for a channel. Note that removing the release_channel field from your config will cause Terraform to stop managing your cluster's release channel, but will not unenroll it. Instead, use the "UNSPECIFIED" channel. Structure is [documented below](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/container_cluster#nested_release_channel).
> ...
>
> The release_channel block supports:
> 
> [channel](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/container_cluster#channel-1) - (Required) The 
> selected release channel. Accepted values are:
>
> UNSPECIFIED: Not set.
>
> RAPID: Weekly upgrade cadence; Early testers and developers who requires new features.
>
> REGULAR: Multiple per month upgrade cadence; Production users who need features not yet offered in the Stable channel.
>
> STABLE: Every few months upgrade cadence; Production users who need stability above all else, and for whom frequent upgrades are too risky.
>
> EXTENDED: GKE provides extended support for Kubernetes minor versions through the Extended channel. With this channel, you can stay on a minor version for up to 24 months.


So IMHO that should not have happened. Instead, it should show the same behaviour than in [production](https://ci.blink.sv/teams/galoy/pipelines/blink-deployments/jobs/galoy-bbw-platform/builds/212): Refusing the update.

If we would have set something else than `UNSPECIFIED`, the version which would get deployed would be specified here:
https://cloud.google.com/kubernetes-engine/docs/release-notes


## Appendix: some details about the prod cluster



Today we also had an unplanned upgrade attempt on the prod cluster in this [job](https://ci.blink.sv/teams/galoy/pipelines/blink-deployments/jobs/galoy-bbw-platform/builds/214) but it properly refused to upgrade:
```
│ Error: googleapi: Error 400: Master version "1.30.9-gke.1231000" is unsupported.
│ Details:
│ [
│   {
│     "@type": "type.googleapis.com/google.rpc.RequestInfo",
│     "requestId": "0xceb0befd3594e145"
│   }
│ ]
│ , badRequest
│ 
│   with module.platform.google_container_cluster.primary,
│   on ../../../modules/infra/vendor/tf/platform/gcp/kube.tf line 11, in resource "google_container_cluster" "primary":
│   11: resource "google_container_cluster" "primary" {
```

Some more details:


It has been triggered by the resource `galoy-bbw-infra` with this [commit](https://github.com/search?q=org%3Ablinkbitcoin+b96828c8fa25adce8a69dc2f550925393844aa1b&type=commits).
Investigating the executions of that job revealed that this upgrade with the same error-message has been tried already in [#213](https://ci.blink.sv/teams/galoy/pipelines/blink-deployments/jobs/galoy-bbw-platform/builds/213) (march 14, ) and [#212](https://ci.blink.sv/teams/galoy/pipelines/blink-deployments/jobs/galoy-bbw-platform/builds/212) (march 17).
Relevant is the first attempt, #212 with the commit [b96828c8fa25adce8a69dc2f550925393844aa1b](https://github.com/search?q=org%3Ablinkbitcoin+b623bc95b6950007c7a3f65139303c2cdec37767&type=commits) which has been approved by this [PR](https://github.com/blinkbitcoin/blink-deployments/pull/7843). The relevant changes were [this](https://github.com/blinkbitcoin/blink-deployments/compare/6ef7c8e83...2fc5df45a).


# Conclusion

It's still unclear why the staging cluster behaved differently than the production one. Potential candidates are terraform provider versions or tofu vs. terraform providers.




